#!/bin/bash

# Script de correction permanente pour Odoo 13 - ERR_CONTENT_LENGTH_MISMATCH
# Ce script configure Nginx comme reverse proxy pour Odoo

echo "🔧 === CORRECTION PERMANENTE ODOO 13 ==="
echo "Configuration de Nginx comme reverse proxy"
echo ""

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si le script est exécuté en tant que root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "Ce script doit être exécuté en tant que root"
        echo "Utilisez: sudo $0"
        exit 1
    fi
}

# Installer Nginx
install_nginx() {
    log_info "Vérification de l'installation de Nginx..."
    
    if command -v nginx &> /dev/null; then
        log_success "Nginx est déjà installé"
        nginx -v
        return 0
    fi
    
    log_info "Installation de Nginx..."
    
    # Détecter la distribution
    if [[ -f /etc/debian_version ]]; then
        apt update
        apt install -y nginx
    elif [[ -f /etc/redhat-release ]]; then
        yum install -y nginx || dnf install -y nginx
    else
        log_error "Distribution non supportée. Installez Nginx manuellement."
        return 1
    fi
    
    if command -v nginx &> /dev/null; then
        log_success "Nginx installé avec succès"
        return 0
    else
        log_error "Échec de l'installation de Nginx"
        return 1
    fi
}

# Créer la configuration Nginx pour Odoo
create_nginx_config() {
    local server_ip="$1"
    local config_file="/etc/nginx/sites-available/odoo13"
    
    log_info "Création de la configuration Nginx pour Odoo..."
    
    cat > "$config_file" << EOF
# Configuration Nginx pour Odoo 13
# Résout le problème ERR_CONTENT_LENGTH_MISMATCH

# Odoo servers
upstream odoo {
    server 127.0.0.1:8069;
}

upstream odoochat {
    server 127.0.0.1:8072;
}

server {
    listen 80;
    server_name $server_ip;

    # Timeouts
    proxy_read_timeout 720s;
    proxy_connect_timeout 720s;
    proxy_send_timeout 720s;

    # Proxy headers
    proxy_set_header X-Forwarded-Host \$host;
    proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto \$scheme;
    proxy_set_header X-Real-IP \$remote_addr;

    # Log files
    access_log /var/log/nginx/odoo.access.log;
    error_log /var/log/nginx/odoo.error.log;

    # Handle longpoll requests
    location /longpolling {
        proxy_pass http://odoochat;
    }

    # Handle / requests
    location / {
        proxy_redirect off;
        proxy_pass http://odoo;
    }

    # Cache static files
    location ~* /web/static/ {
        proxy_cache_valid 200 90m;
        proxy_buffering on;
        expires 864000;
        proxy_pass http://odoo;
    }

    # Gzip compression
    gzip_types text/css text/less text/plain text/xml application/xml application/json application/javascript;
    gzip on;
}
EOF

    if [[ $? -eq 0 ]]; then
        log_success "Configuration Nginx créée: $config_file"
        return 0
    else
        log_error "Échec de la création de la configuration Nginx"
        return 1
    fi
}

# Activer la configuration Nginx
enable_nginx_config() {
    local config_file="/etc/nginx/sites-available/odoo13"
    local enabled_file="/etc/nginx/sites-enabled/odoo13"
    
    log_info "Activation de la configuration Nginx..."
    
    # Créer le lien symbolique
    if [[ ! -L "$enabled_file" ]]; then
        ln -s "$config_file" "$enabled_file"
        log_success "Configuration activée"
    else
        log_info "Configuration déjà activée"
    fi
    
    # Tester la configuration
    log_info "Test de la configuration Nginx..."
    if nginx -t; then
        log_success "Configuration Nginx valide"
        return 0
    else
        log_error "Configuration Nginx invalide"
        return 1
    fi
}

# Configurer Odoo pour le proxy
configure_odoo_for_proxy() {
    log_info "Configuration d'Odoo pour le reverse proxy..."
    
    # Trouver le fichier de configuration Odoo
    POSSIBLE_PATHS=(
        "/etc/odoo/odoo.conf"
        "/etc/odoo13/odoo.conf"
        "/etc/odoo.conf"
        "/opt/odoo/odoo.conf"
        "/opt/odoo13/odoo.conf"
    )
    
    local config_file=""
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [[ -f "$path" ]]; then
            config_file="$path"
            break
        fi
    done
    
    if [[ -z "$config_file" ]]; then
        log_error "Fichier de configuration Odoo non trouvé"
        return 1
    fi
    
    log_info "Fichier de configuration Odoo: $config_file"
    
    # Créer une sauvegarde
    cp "$config_file" "${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    # Ajouter/modifier les paramètres proxy
    if ! grep -q "proxy_mode" "$config_file"; then
        echo "" >> "$config_file"
        echo "# Configuration reverse proxy" >> "$config_file"
        echo "proxy_mode = True" >> "$config_file"
    else
        sed -i 's/^proxy_mode\s*=.*/proxy_mode = True/' "$config_file"
    fi
    
    if ! grep -q "xmlrpc_interface" "$config_file"; then
        echo "xmlrpc_interface = 127.0.0.1" >> "$config_file"
    else
        sed -i 's/^xmlrpc_interface\s*=.*/xmlrpc_interface = 127.0.0.1/' "$config_file"
    fi
    
    if ! grep -q "netrpc_interface" "$config_file"; then
        echo "netrpc_interface = 127.0.0.1" >> "$config_file"
    else
        sed -i 's/^netrpc_interface\s*=.*/netrpc_interface = 127.0.0.1/' "$config_file"
    fi
    
    # Réactiver les workers maintenant que nous avons un proxy
    if grep -q "^#workers\s*=" "$config_file"; then
        sed -i 's/^#workers\s*=/workers =/' "$config_file"
        log_success "Workers réactivés"
    elif ! grep -q "^workers\s*=" "$config_file"; then
        echo "workers = 3" >> "$config_file"
        log_success "Workers configurés"
    fi
    
    log_success "Configuration Odoo mise à jour"
    return 0
}

# Redémarrer les services
restart_services() {
    log_info "Redémarrage des services..."
    
    # Redémarrer Nginx
    log_info "Redémarrage de Nginx..."
    if systemctl restart nginx; then
        log_success "Nginx redémarré"
    else
        log_error "Échec du redémarrage de Nginx"
        return 1
    fi
    
    # Redémarrer Odoo
    SERVICES=("odoo" "odoo13" "odoo-server" "odoo13-server")
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-enabled --quiet "$service" 2>/dev/null; then
            log_info "Redémarrage de $service..."
            if systemctl restart "$service"; then
                log_success "Service $service redémarré"
                sleep 3
                if systemctl is-active --quiet "$service"; then
                    log_success "Service $service est actif"
                    return 0
                fi
            fi
        fi
    done
    
    log_warning "Redémarrez Odoo manuellement"
    return 0
}

# Fonction principale
main() {
    echo ""
    log_info "Début de la configuration permanente..."
    
    # Vérifier les privilèges root
    check_root
    
    # Demander l'IP du serveur
    echo ""
    read -p "Entrez l'adresse IP du serveur Odoo (ex: **************): " server_ip
    
    if [[ -z "$server_ip" ]]; then
        log_error "Adresse IP requise"
        exit 1
    fi
    
    echo ""
    log_info "Configuration pour le serveur: $server_ip"
    
    # Installer Nginx
    if ! install_nginx; then
        exit 1
    fi
    
    # Créer la configuration Nginx
    if ! create_nginx_config "$server_ip"; then
        exit 1
    fi
    
    # Activer la configuration
    if ! enable_nginx_config; then
        exit 1
    fi
    
    # Configurer Odoo
    if ! configure_odoo_for_proxy; then
        exit 1
    fi
    
    # Redémarrer les services
    restart_services
    
    echo ""
    echo "🎉 === CONFIGURATION PERMANENTE TERMINÉE ==="
    log_success "Nginx configuré comme reverse proxy"
    log_success "Odoo configuré pour utiliser le proxy"
    log_success "Workers réactivés pour de meilleures performances"
    echo ""
    log_info "Testez maintenant votre connexion à Odoo"
    log_info "URL d'accès: http://$server_ip"
    echo ""
}

# Exécuter le script principal
main "$@"
