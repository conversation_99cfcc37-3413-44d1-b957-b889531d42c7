#!/bin/bash

# Script de correction temporaire pour Odoo 13 - ERR_CONTENT_LENGTH_MISMATCH
# Ce script désactive les workers pour résoudre temporairement le problème

echo "🔧 === CORRECTION TEMPORAIRE ODOO 13 ==="
echo "Résolution du problème ERR_CONTENT_LENGTH_MISMATCH"
echo ""

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier si le script est exécuté en tant que root
if [[ $EUID -eq 0 ]]; then
   log_warning "Ce script est exécuté en tant que root. Assurez-vous que c'est nécessaire."
fi

# Chemins possibles pour le fichier de configuration Odoo
POSSIBLE_PATHS=(
    "/etc/odoo/odoo.conf"
    "/etc/odoo13/odoo.conf"
    "/etc/odoo.conf"
    "/opt/odoo/odoo.conf"
    "/opt/odoo13/odoo.conf"
    "/home/<USER>/odoo.conf"
    "/usr/local/etc/odoo.conf"
)

# Fonction pour trouver le fichier de configuration Odoo
find_odoo_config() {
    log_info "Recherche du fichier de configuration Odoo..."
    
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [[ -f "$path" ]]; then
            log_success "Fichier de configuration trouvé: $path"
            echo "$path"
            return 0
        fi
    done
    
    log_error "Fichier de configuration Odoo non trouvé dans les emplacements standards"
    echo ""
    echo "Emplacements vérifiés:"
    for path in "${POSSIBLE_PATHS[@]}"; do
        echo "  - $path"
    done
    echo ""
    echo "Veuillez spécifier manuellement le chemin du fichier odoo.conf:"
    read -p "Chemin complet vers odoo.conf: " manual_path
    
    if [[ -f "$manual_path" ]]; then
        echo "$manual_path"
        return 0
    else
        log_error "Fichier non trouvé: $manual_path"
        return 1
    fi
}

# Fonction pour sauvegarder le fichier de configuration
backup_config() {
    local config_file="$1"
    local backup_file="${config_file}.backup.$(date +%Y%m%d_%H%M%S)"
    
    log_info "Création d'une sauvegarde: $backup_file"
    cp "$config_file" "$backup_file"
    
    if [[ $? -eq 0 ]]; then
        log_success "Sauvegarde créée avec succès"
        return 0
    else
        log_error "Échec de la création de la sauvegarde"
        return 1
    fi
}

# Fonction pour désactiver les workers
disable_workers() {
    local config_file="$1"
    
    log_info "Désactivation des workers dans $config_file"
    
    # Vérifier si la ligne workers existe
    if grep -q "^workers\s*=" "$config_file"; then
        log_info "Ligne 'workers' trouvée, commentaire en cours..."
        sed -i 's/^workers\s*=/#workers =/' "$config_file"
        log_success "Ligne 'workers' commentée"
    elif grep -q "^#workers\s*=" "$config_file"; then
        log_info "Ligne 'workers' déjà commentée"
    else
        log_warning "Aucune ligne 'workers' trouvée dans le fichier"
    fi
    
    # Afficher les lignes modifiées
    echo ""
    log_info "Configuration actuelle des workers:"
    grep -n "workers" "$config_file" || echo "Aucune ligne 'workers' trouvée"
}

# Fonction pour redémarrer Odoo
restart_odoo() {
    log_info "Tentative de redémarrage du service Odoo..."
    
    # Services possibles
    SERVICES=("odoo" "odoo13" "odoo-server" "odoo13-server")
    
    for service in "${SERVICES[@]}"; do
        if systemctl is-active --quiet "$service"; then
            log_info "Service actif trouvé: $service"
            log_info "Redémarrage de $service..."
            
            if systemctl restart "$service"; then
                log_success "Service $service redémarré avec succès"
                
                # Vérifier le statut
                sleep 2
                if systemctl is-active --quiet "$service"; then
                    log_success "Service $service est actif"
                    return 0
                else
                    log_error "Service $service n'est pas actif après le redémarrage"
                    systemctl status "$service" --no-pager -l
                fi
            else
                log_error "Échec du redémarrage de $service"
            fi
            return 1
        fi
    done
    
    log_warning "Aucun service Odoo actif trouvé"
    log_info "Services vérifiés: ${SERVICES[*]}"
    echo ""
    log_info "Vous devrez redémarrer Odoo manuellement"
    return 1
}

# Fonction principale
main() {
    echo ""
    log_info "Début de la correction temporaire..."
    
    # Trouver le fichier de configuration
    CONFIG_FILE=$(find_odoo_config)
    if [[ $? -ne 0 ]]; then
        log_error "Impossible de continuer sans le fichier de configuration"
        exit 1
    fi
    
    echo ""
    log_info "Fichier de configuration: $CONFIG_FILE"
    
    # Créer une sauvegarde
    if ! backup_config "$CONFIG_FILE"; then
        log_error "Impossible de créer une sauvegarde. Arrêt du script."
        exit 1
    fi
    
    # Désactiver les workers
    disable_workers "$CONFIG_FILE"
    
    echo ""
    log_info "Modification terminée. Redémarrage du service..."
    
    # Redémarrer Odoo
    restart_odoo
    
    echo ""
    echo "🎉 === CORRECTION TEMPORAIRE TERMINÉE ==="
    log_success "Les workers ont été désactivés"
    log_info "Testez maintenant votre connexion à Odoo"
    echo ""
    log_warning "IMPORTANT: Cette solution est temporaire"
    log_info "Pour une solution permanente, configurez Nginx comme reverse proxy"
    echo ""
}

# Exécuter le script principal
main "$@"
