# Solution pour ERR_CONTENT_LENGTH_MISMATCH dans Odoo 13

## Problème identifié
L'erreur `ERR_CONTENT_LENGTH_MISMATCH` et l'écran blanc dans Odoo 13 sont causés par l'utilisation de workers (workers > 0) sans reverse proxy.

## Cause principale
**Les workers dans Odoo 13 ne fonctionnent pas correctement sans un reverse proxy comme Nginx**. C'est un problème connu et documenté dans la communauté Odoo.

## Solutions proposées

### Solution 1: Temporaire - Désactiver les workers

#### Étapes:
1. Localiser le fichier de configuration Odoo (`odoo.conf`)
2. Commenter ou supprimer la ligne `workers = X`
3. Redémarrer le service Odoo

#### Configuration à modifier:
```ini
[options]
admin_passwd = votre_mot_de_passe
db_host = **************
db_port = 5432
db_user = odoo
db_password = votre_mot_de_passe_db
# workers = 3  # COMMENTEZ CETTE LIGNE
```

### Solution 2: Permanente - Configurer Nginx comme reverse proxy

#### Avantages:
- Résout définitivement le problème ERR_CONTENT_LENGTH_MISMATCH
- Améliore les performances
- Permet l'utilisation des workers pour une meilleure stabilité
- Gestion optimisée des fichiers statiques

#### Configuration Nginx recommandée:
```nginx
# Odoo servers
upstream odoo {
    server 127.0.0.1:8069;
}

upstream odoochat {
    server 127.0.0.1:8072;
}

server {
    listen 80;
    server_name **************;  # Votre IP serveur

    proxy_read_timeout 720s;
    proxy_connect_timeout 720s;
    proxy_send_timeout 720s;

    # Proxy headers
    proxy_set_header X-Forwarded-Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;

    # Log files
    access_log /var/log/nginx/odoo.access.log;
    error_log /var/log/nginx/odoo.error.log;

    # Handle longpoll requests
    location /longpolling {
        proxy_pass http://odoochat;
    }

    # Handle / requests
    location / {
        proxy_redirect off;
        proxy_pass http://odoo;
    }

    # Cache static files
    location ~* /web/static/ {
        proxy_cache_valid 200 90m;
        proxy_buffering on;
        expires 864000;
        proxy_pass http://odoo;
    }

    # Gzip compression
    gzip_types text/css text/less text/plain text/xml application/xml application/json application/javascript;
    gzip on;
}
```

## Instructions d'implémentation

### Étape 1: Test rapide (Solution temporaire)
1. Connectez-vous au serveur Odoo (**************)
2. Localisez le fichier de configuration Odoo
3. Commentez la ligne `workers = X`
4. Redémarrez Odoo
5. Testez la connexion depuis votre application

### Étape 2: Solution permanente
1. Installer Nginx sur le serveur
2. Configurer le reverse proxy
3. Modifier la configuration Odoo
4. Redémarrer les services

## Commandes pour l'implémentation

### Installation Nginx (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install nginx
```

### Configuration et activation:
```bash
# Créer le fichier de configuration
sudo nano /etc/nginx/sites-available/odoo13

# Activer la configuration
sudo ln -s /etc/nginx/sites-available/odoo13 /etc/nginx/sites-enabled/

# Tester la configuration
sudo nginx -t

# Redémarrer Nginx
sudo systemctl restart nginx

# Redémarrer Odoo
sudo systemctl restart odoo13
```

## Scripts automatisés créés

### 1. Test de connexion
```bash
./test-odoo13-connection.sh
```
- Vérifie l'état des serveurs Odoo
- Teste les assets web
- Détecte les problèmes ERR_CONTENT_LENGTH_MISMATCH

### 2. Correction temporaire
```bash
./fix-odoo13-temporary.sh
```
- Désactive automatiquement les workers
- Sauvegarde la configuration
- Redémarre Odoo

### 3. Correction permanente
```bash
sudo ./fix-odoo13-permanent.sh
```
- Installe et configure Nginx
- Configure Odoo pour le reverse proxy
- Réactive les workers

## Instructions d'utilisation

### Étape 1: Diagnostic
```bash
# Tester la connexion actuelle
./test-odoo13-connection.sh
```

### Étape 2: Correction rapide
```bash
# Si vous voulez une solution immédiate
./fix-odoo13-temporary.sh
```

### Étape 3: Solution permanente (recommandée)
```bash
# Pour une solution complète et optimisée
sudo ./fix-odoo13-permanent.sh
```

## Résultat attendu
Après l'implémentation de la solution, vous devriez:
- ✅ Ne plus avoir d'erreurs ERR_CONTENT_LENGTH_MISMATCH
- ✅ Voir l'interface Odoo se charger correctement
- ✅ Avoir des performances améliorées
- ✅ Pouvoir utiliser les workers pour une meilleure stabilité

## Prochaines étapes
Une fois la solution implémentée, nous pourrons:
1. Optimiser davantage la configuration
2. Configurer HTTPS si nécessaire
3. Ajuster les paramètres de performance selon vos besoins
