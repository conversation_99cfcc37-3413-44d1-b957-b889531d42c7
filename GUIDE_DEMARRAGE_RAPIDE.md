# 🚀 Guide de Démarrage Rapide - Correction Odoo 13

## 🎯 Problème identifié
Votre serveur Odoo 13 (`**************`) est accessible mais présente des erreurs `ERR_CONTENT_LENGTH_MISMATCH` causées par l'utilisation de workers sans reverse proxy.

## ⚡ Solution Rapide (5 minutes)

### Étape 1: Connexion au serveur
```bash
# Connectez-vous au serveur Odoo
ssh utilisateur@**************
```

### Étape 2: Télécharger le script de correction
```bash
# Télécharger le script depuis votre machine locale
# Ou créer le script directement sur le serveur
```

### Étape 3: Exécuter la correction temporaire
```bash
# Rendre le script exécutable
chmod +x fix-odoo13-temporary.sh

# Exécuter la correction
./fix-odoo13-temporary.sh
```

## 🔧 Ce que fait le script automatiquement

1. **Localise** le fichier de configuration Odoo
2. **Sauvegarde** la configuration actuelle
3. **Désactive** les workers (cause du problème)
4. **Redémarre** le service Odoo
5. **Vérifie** que tout fonctionne

## ✅ Résultat attendu

Après exécution du script, vous devriez voir :
- ✅ Plus d'erreurs `ERR_CONTENT_LENGTH_MISMATCH`
- ✅ Interface Odoo qui se charge correctement
- ✅ Assets CSS/JS accessibles

## 🧪 Test de vérification

```bash
# Tester la connexion après correction
./test-odoo13-connection.sh
```

## 📋 Si vous voulez faire manuellement

### 1. Localiser le fichier de configuration
```bash
# Emplacements possibles
sudo find /etc -name "odoo.conf" 2>/dev/null
sudo find /opt -name "odoo.conf" 2>/dev/null
```

### 2. Modifier la configuration
```bash
# Éditer le fichier (exemple: /etc/odoo/odoo.conf)
sudo nano /etc/odoo/odoo.conf

# Commenter la ligne workers
# workers = 3  # <- Ajouter # au début
```

### 3. Redémarrer Odoo
```bash
# Essayer ces commandes selon votre installation
sudo systemctl restart odoo
sudo systemctl restart odoo13
sudo service odoo restart
```

## 🏆 Solution Permanente (Recommandée)

Une fois que la solution temporaire fonctionne, implémentez la solution permanente :

```bash
# Exécuter le script de configuration Nginx
sudo ./fix-odoo13-permanent.sh
```

Cette solution :
- ✅ Installe et configure Nginx
- ✅ Réactive les workers pour de meilleures performances
- ✅ Optimise la configuration pour la production

## 🆘 En cas de problème

### Si le script ne trouve pas la configuration :
```bash
# Chercher manuellement
sudo find / -name "*.conf" | grep odoo
ps aux | grep odoo  # Voir le processus et ses arguments
```

### Si Odoo ne redémarre pas :
```bash
# Vérifier les logs
sudo journalctl -u odoo -f
sudo tail -f /var/log/odoo/odoo-server.log
```

### Si vous ne pouvez pas vous connecter au serveur :
1. Vérifiez que le serveur est allumé
2. Vérifiez la connectivité réseau
3. Contactez l'administrateur système

## 📞 Support

Si vous rencontrez des difficultés :
1. Exécutez `./test-odoo13-connection.sh` pour diagnostiquer
2. Vérifiez les logs d'erreur
3. Documentez les messages d'erreur exacts

## 🎉 Succès !

Une fois la correction appliquée, votre application Electron devrait pouvoir se connecter à Odoo sans erreurs `ERR_CONTENT_LENGTH_MISMATCH`.

---

**Temps estimé :** 5-10 minutes  
**Niveau de difficulté :** Facile  
**Prérequis :** Accès SSH au serveur Odoo
