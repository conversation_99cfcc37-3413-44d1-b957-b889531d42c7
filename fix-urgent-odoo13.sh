#!/bin/bash

# CORRECTION URGENTE ODOO 13 - ERR_CONTENT_LENGTH_MISMATCH
# Ce script désactive immédiatement les workers pour résoudre le problème

echo "🚨 === CORRECTION URGENTE ODOO 13 ==="
echo "Résolution immédiate du problème ERR_CONTENT_LENGTH_MISMATCH"
echo ""

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Fonction pour trouver et modifier la configuration Odoo
fix_odoo_config() {
    log_info "🔍 Recherche de la configuration Odoo..."
    
    # Emplacements possibles
    local config_paths=(
        "/etc/odoo/odoo.conf"
        "/etc/odoo13/odoo.conf"
        "/etc/odoo.conf"
        "/opt/odoo/odoo.conf"
        "/opt/odoo13/odoo.conf"
        "/home/<USER>/odoo.conf"
        "/usr/local/etc/odoo.conf"
        "/etc/odoo-server.conf"
        "/etc/odoo13-server.conf"
    )
    
    local config_file=""
    
    # Chercher le fichier de configuration
    for path in "${config_paths[@]}"; do
        if [[ -f "$path" ]]; then
            config_file="$path"
            log_success "✅ Configuration trouvée: $path"
            break
        fi
    done
    
    # Si pas trouvé, chercher avec find
    if [[ -z "$config_file" ]]; then
        log_info "Recherche étendue..."
        config_file=$(find /etc /opt /home -name "*.conf" 2>/dev/null | grep -i odoo | head -1)
        
        if [[ -n "$config_file" && -f "$config_file" ]]; then
            log_success "✅ Configuration trouvée: $config_file"
        else
            log_error "❌ Configuration Odoo non trouvée"
            return 1
        fi
    fi
    
    # Sauvegarder la configuration
    local backup_file="${config_file}.backup.urgent.$(date +%Y%m%d_%H%M%S)"
    log_info "💾 Sauvegarde: $backup_file"
    cp "$config_file" "$backup_file"
    
    # Afficher la configuration actuelle des workers
    log_info "📋 Configuration actuelle des workers:"
    grep -n "workers" "$config_file" || echo "Aucune ligne 'workers' trouvée"
    
    echo ""
    log_info "🔧 Modification de la configuration..."
    
    # Désactiver tous les workers
    if grep -q "^workers\s*=" "$config_file"; then
        # Commenter la ligne existante
        sed -i 's/^workers\s*=/#&/' "$config_file"
        log_success "✅ Ligne 'workers' commentée"
    fi
    
    # Ajouter workers = 0 pour être sûr
    if ! grep -q "^workers\s*=\s*0" "$config_file"; then
        echo "" >> "$config_file"
        echo "# Configuration urgente - workers désactivés" >> "$config_file"
        echo "workers = 0" >> "$config_file"
        log_success "✅ workers = 0 ajouté"
    fi
    
    # Vérifier la nouvelle configuration
    echo ""
    log_info "📋 Nouvelle configuration des workers:"
    grep -n "workers" "$config_file"
    
    return 0
}

# Fonction pour redémarrer Odoo
restart_odoo_service() {
    log_info "🔄 Redémarrage du service Odoo..."
    
    # Services possibles
    local services=("odoo" "odoo13" "odoo-server" "odoo13-server" "odoo.service" "odoo13.service")
    local restarted=false
    
    for service in "${services[@]}"; do
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            log_info "🔄 Redémarrage de $service..."
            
            if systemctl restart "$service"; then
                log_success "✅ Service $service redémarré"
                
                # Attendre et vérifier
                sleep 3
                if systemctl is-active --quiet "$service"; then
                    log_success "✅ Service $service est actif"
                    restarted=true
                    break
                else
                    log_error "❌ Service $service n'est pas actif après redémarrage"
                    # Afficher les logs d'erreur
                    echo "📋 Logs d'erreur:"
                    systemctl status "$service" --no-pager -l | tail -10
                fi
            else
                log_error "❌ Échec du redémarrage de $service"
            fi
        fi
    done
    
    if [[ "$restarted" == false ]]; then
        log_warning "⚠️ Aucun service Odoo actif trouvé ou redémarrage échoué"
        log_info "Services vérifiés: ${services[*]}"
        
        # Essayer de trouver le processus Odoo
        local odoo_pid=$(pgrep -f "odoo" | head -1)
        if [[ -n "$odoo_pid" ]]; then
            log_info "🔍 Processus Odoo trouvé (PID: $odoo_pid)"
            log_warning "⚠️ Vous devrez redémarrer Odoo manuellement"
            echo "Commandes possibles:"
            echo "  sudo kill -HUP $odoo_pid  # Redémarrage gracieux"
            echo "  sudo kill $odoo_pid && sudo systemctl start odoo  # Redémarrage complet"
        else
            log_error "❌ Aucun processus Odoo trouvé"
        fi
        
        return 1
    fi
    
    return 0
}

# Fonction pour tester la connexion
test_connection() {
    log_info "🧪 Test de connexion..."
    
    local server_ip="**************"
    local url="http://$server_ip:8069"
    
    # Test de base
    local response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 10 "$url" 2>/dev/null)
    
    if [[ "$response" =~ ^[23] ]]; then
        log_success "✅ Serveur accessible (HTTP: $response)"
        
        # Test des assets critiques
        local assets=(
            "$url/web/static/src/css/base.css"
            "$url/web/static/src/js/boot.js"
        )
        
        local success_count=0
        for asset in "${assets[@]}"; do
            local asset_response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 5 "$asset" 2>/dev/null)
            if [[ "$asset_response" =~ ^[23] ]]; then
                ((success_count++))
                log_success "✅ Asset accessible: $(basename "$asset")"
            else
                log_warning "⚠️ Asset problématique: $(basename "$asset") (HTTP: $asset_response)"
            fi
        done
        
        if [[ $success_count -eq ${#assets[@]} ]]; then
            log_success "🎉 Tous les assets sont accessibles"
            return 0
        else
            log_warning "⚠️ Certains assets ont encore des problèmes"
            return 1
        fi
    else
        log_error "❌ Serveur non accessible (HTTP: $response)"
        return 2
    fi
}

# Fonction principale
main() {
    echo ""
    log_info "🚀 Début de la correction urgente..."
    
    # Vérifier les privilèges
    if [[ $EUID -ne 0 ]]; then
        log_warning "⚠️ Ce script n'est pas exécuté en tant que root"
        log_info "Certaines opérations peuvent échouer"
        echo ""
    fi
    
    # Étape 1: Modifier la configuration
    if ! fix_odoo_config; then
        log_error "❌ Échec de la modification de la configuration"
        exit 1
    fi
    
    echo ""
    
    # Étape 2: Redémarrer Odoo
    if ! restart_odoo_service; then
        log_warning "⚠️ Problème lors du redémarrage"
        log_info "La configuration a été modifiée, mais le service doit être redémarré manuellement"
    fi
    
    echo ""
    
    # Étape 3: Tester la connexion
    log_info "⏳ Attente de 5 secondes pour que le service démarre..."
    sleep 5
    
    test_connection
    
    echo ""
    echo "🎯 === CORRECTION URGENTE TERMINÉE ==="
    log_success "✅ Configuration modifiée: workers = 0"
    log_info "🧪 Testez maintenant votre application Electron"
    echo ""
    log_info "📋 Si le problème persiste:"
    echo "  1. Vérifiez que le service Odoo a redémarré"
    echo "  2. Consultez les logs: sudo journalctl -u odoo -f"
    echo "  3. Testez manuellement: curl http://**************:8069"
    echo ""
}

# Exécuter le script
main "$@"
