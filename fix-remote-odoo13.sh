#!/bin/bash

# CORRECTION DISTANTE ODOO 13 - ERR_CONTENT_LENGTH_MISMATCH
# Ce script se connecte au serveur et applique la correction automatiquement

echo "🌐 === CORRECTION DISTANTE ODOO 13 ==="
echo "Correction automatique via SSH"
echo ""

# Couleurs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Configuration par défaut
DEFAULT_SERVER="**************"
DEFAULT_USER="odoo"

# Script de correction à exécuter sur le serveur distant
REMOTE_SCRIPT='#!/bin/bash

echo "🔧 Correction Odoo sur le serveur..."

# Trouver la configuration Odoo
CONFIG_PATHS=(
    "/etc/odoo/odoo.conf"
    "/etc/odoo13/odoo.conf" 
    "/etc/odoo.conf"
    "/opt/odoo/odoo.conf"
    "/opt/odoo13/odoo.conf"
    "/home/<USER>/odoo.conf"
    "/etc/odoo-server.conf"
)

CONFIG_FILE=""
for path in "${CONFIG_PATHS[@]}"; do
    if [[ -f "$path" ]]; then
        CONFIG_FILE="$path"
        echo "✅ Configuration trouvée: $path"
        break
    fi
done

if [[ -z "$CONFIG_FILE" ]]; then
    CONFIG_FILE=$(find /etc /opt /home -name "*.conf" 2>/dev/null | grep -i odoo | head -1)
    if [[ -n "$CONFIG_FILE" && -f "$CONFIG_FILE" ]]; then
        echo "✅ Configuration trouvée: $CONFIG_FILE"
    else
        echo "❌ Configuration Odoo non trouvée"
        exit 1
    fi
fi

# Sauvegarder
BACKUP_FILE="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
echo "💾 Sauvegarde: $BACKUP_FILE"
sudo cp "$CONFIG_FILE" "$BACKUP_FILE"

# Modifier la configuration
echo "🔧 Désactivation des workers..."

# Commenter les workers existants
sudo sed -i "s/^workers\s*=/#&/" "$CONFIG_FILE"

# Ajouter workers = 0
if ! grep -q "^workers\s*=\s*0" "$CONFIG_FILE"; then
    echo "" | sudo tee -a "$CONFIG_FILE"
    echo "# Correction ERR_CONTENT_LENGTH_MISMATCH" | sudo tee -a "$CONFIG_FILE"
    echo "workers = 0" | sudo tee -a "$CONFIG_FILE"
fi

echo "✅ Configuration modifiée"

# Redémarrer Odoo
echo "🔄 Redémarrage du service Odoo..."
SERVICES=("odoo" "odoo13" "odoo-server" "odoo13-server")

for service in "${SERVICES[@]}"; do
    if sudo systemctl is-active --quiet "$service" 2>/dev/null; then
        echo "🔄 Redémarrage de $service..."
        if sudo systemctl restart "$service"; then
            echo "✅ Service $service redémarré"
            sleep 3
            if sudo systemctl is-active --quiet "$service"; then
                echo "✅ Service $service est actif"
                break
            fi
        fi
    fi
done

echo "🎉 Correction terminée sur le serveur"
'

# Fonction pour exécuter la correction distante
execute_remote_fix() {
    local server_ip="$1"
    local username="$2"
    
    log_info "🌐 Connexion à $username@$server_ip..."
    
    # Tester la connexion SSH
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes "$username@$server_ip" exit 2>/dev/null; then
        log_error "❌ Impossible de se connecter via SSH"
        log_info "Vérifiez:"
        echo "  - L'adresse IP du serveur"
        echo "  - Le nom d'utilisateur"
        echo "  - Les clés SSH ou mot de passe"
        echo "  - La connectivité réseau"
        return 1
    fi
    
    log_success "✅ Connexion SSH établie"
    
    # Exécuter le script de correction sur le serveur distant
    log_info "🔧 Exécution de la correction sur le serveur..."
    
    if ssh "$username@$server_ip" "$REMOTE_SCRIPT"; then
        log_success "✅ Correction exécutée avec succès"
        return 0
    else
        log_error "❌ Échec de la correction distante"
        return 1
    fi
}

# Fonction pour tester la connexion après correction
test_remote_connection() {
    local server_ip="$1"
    
    log_info "🧪 Test de la connexion Odoo..."
    
    local url="http://$server_ip:8069"
    local response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 10 "$url" 2>/dev/null)
    
    if [[ "$response" =~ ^[23] ]]; then
        log_success "✅ Serveur Odoo accessible (HTTP: $response)"
        
        # Test d'un asset critique
        local asset_url="$url/web/static/src/js/boot.js"
        local asset_response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 5 "$asset_url" 2>/dev/null)
        
        if [[ "$asset_response" =~ ^[23] ]]; then
            log_success "✅ Assets JavaScript accessibles"
            log_success "🎉 Problème ERR_CONTENT_LENGTH_MISMATCH résolu !"
            return 0
        else
            log_warning "⚠️ Certains assets ont encore des problèmes"
            return 1
        fi
    else
        log_error "❌ Serveur Odoo non accessible"
        return 2
    fi
}

# Fonction principale
main() {
    echo ""
    log_info "🚀 Début de la correction distante..."
    
    # Demander les informations de connexion
    echo ""
    read -p "Adresse IP du serveur Odoo [$DEFAULT_SERVER]: " server_ip
    server_ip=${server_ip:-$DEFAULT_SERVER}
    
    read -p "Nom d'utilisateur SSH [$DEFAULT_USER]: " username
    username=${username:-$DEFAULT_USER}
    
    echo ""
    log_info "Configuration:"
    echo "  Serveur: $server_ip"
    echo "  Utilisateur: $username"
    echo ""
    
    read -p "Continuer avec cette configuration ? (y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        log_info "Correction annulée"
        exit 0
    fi
    
    echo ""
    
    # Exécuter la correction
    if execute_remote_fix "$server_ip" "$username"; then
        echo ""
        log_info "⏳ Attente de 5 secondes pour que le service redémarre..."
        sleep 5
        
        # Tester la connexion
        test_remote_connection "$server_ip"
        
        echo ""
        echo "🎯 === CORRECTION DISTANTE TERMINÉE ==="
        log_success "✅ Workers désactivés sur le serveur"
        log_info "🧪 Testez maintenant votre application Electron"
        echo ""
        log_info "📋 Si le problème persiste:"
        echo "  1. Connectez-vous au serveur: ssh $username@$server_ip"
        echo "  2. Vérifiez les logs: sudo journalctl -u odoo -f"
        echo "  3. Vérifiez la config: grep workers /etc/odoo*/odoo.conf"
        
    else
        echo ""
        log_error "❌ Échec de la correction distante"
        echo ""
        log_info "📋 Solutions alternatives:"
        echo "  1. Connectez-vous manuellement: ssh $username@$server_ip"
        echo "  2. Transférez le script: scp fix-urgent-odoo13.sh $username@$server_ip:/tmp/"
        echo "  3. Exécutez sur le serveur: /tmp/fix-urgent-odoo13.sh"
    fi
    
    echo ""
}

# Vérifier les prérequis
if ! command -v ssh &> /dev/null; then
    log_error "❌ SSH n'est pas installé"
    echo "Installation requise:"
    echo "  Ubuntu/Debian: sudo apt install openssh-client"
    echo "  CentOS/RHEL: sudo yum install openssh-clients"
    exit 1
fi

if ! command -v curl &> /dev/null; then
    log_error "❌ curl n'est pas installé"
    echo "Installation requise:"
    echo "  Ubuntu/Debian: sudo apt install curl"
    echo "  CentOS/RHEL: sudo yum install curl"
    exit 1
fi

# Exécuter le script principal
main "$@"
