#!/bin/bash

# Script de transfert des fichiers de correction vers le serveur Odoo
# Facilite l'envoi des scripts de correction

echo "📤 === TRANSFERT VERS SERVEUR ODOO ==="
echo "Envoi des scripts de correction"
echo ""

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fichiers à transférer
FILES_TO_TRANSFER=(
    "fix-odoo13-temporary.sh"
    "fix-odoo13-permanent.sh"
    "test-odoo13-connection.sh"
    "SOLUTION_ODOO13_ERR_CONTENT_LENGTH.md"
    "GUIDE_DEMARRAGE_RAPIDE.md"
)

# Fonction pour vérifier les fichiers locaux
check_local_files() {
    log_info "Vérification des fichiers locaux..."
    
    local missing_files=()
    
    for file in "${FILES_TO_TRANSFER[@]}"; do
        if [[ -f "$file" ]]; then
            log_success "✅ $file"
        else
            log_error "❌ $file (manquant)"
            missing_files+=("$file")
        fi
    done
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "Fichiers manquants: ${missing_files[*]}"
        return 1
    fi
    
    log_success "Tous les fichiers sont présents"
    return 0
}

# Fonction pour transférer via SCP
transfer_via_scp() {
    local server_ip="$1"
    local username="$2"
    local destination_path="$3"
    
    log_info "Transfert via SCP vers $username@$server_ip:$destination_path"
    
    # Créer le répertoire de destination si nécessaire
    ssh "$username@$server_ip" "mkdir -p $destination_path" 2>/dev/null
    
    # Transférer chaque fichier
    for file in "${FILES_TO_TRANSFER[@]}"; do
        log_info "Transfert de $file..."
        
        if scp "$file" "$username@$server_ip:$destination_path/"; then
            log_success "✅ $file transféré"
        else
            log_error "❌ Échec du transfert de $file"
            return 1
        fi
    done
    
    # Rendre les scripts exécutables
    log_info "Configuration des permissions..."
    ssh "$username@$server_ip" "chmod +x $destination_path/*.sh" 2>/dev/null
    
    log_success "Transfert terminé avec succès"
    return 0
}

# Fonction pour afficher les instructions de connexion
show_connection_instructions() {
    local server_ip="$1"
    local username="$2"
    local destination_path="$3"
    
    echo ""
    echo "🎯 === INSTRUCTIONS DE CONNEXION ==="
    echo ""
    echo "1. Connectez-vous au serveur :"
    echo "   ssh $username@$server_ip"
    echo ""
    echo "2. Naviguez vers le répertoire :"
    echo "   cd $destination_path"
    echo ""
    echo "3. Exécutez la correction temporaire :"
    echo "   ./fix-odoo13-temporary.sh"
    echo ""
    echo "4. Ou la correction permanente :"
    echo "   sudo ./fix-odoo13-permanent.sh"
    echo ""
    echo "5. Testez la connexion :"
    echo "   ./test-odoo13-connection.sh"
    echo ""
}

# Fonction pour créer un package local
create_local_package() {
    local package_name="odoo13-fix-$(date +%Y%m%d_%H%M%S).tar.gz"
    
    log_info "Création d'un package local: $package_name"
    
    if tar -czf "$package_name" "${FILES_TO_TRANSFER[@]}" 2>/dev/null; then
        log_success "Package créé: $package_name"
        echo ""
        echo "📦 Vous pouvez maintenant transférer ce package manuellement :"
        echo "   scp $package_name utilisateur@serveur:/tmp/"
        echo ""
        echo "Puis sur le serveur :"
        echo "   cd /tmp"
        echo "   tar -xzf $package_name"
        echo "   chmod +x *.sh"
        echo ""
        return 0
    else
        log_error "Échec de la création du package"
        return 1
    fi
}

# Fonction principale
main() {
    echo ""
    log_info "Début du processus de transfert..."
    
    # Vérifier les fichiers locaux
    if ! check_local_files; then
        log_error "Impossible de continuer sans tous les fichiers"
        exit 1
    fi
    
    echo ""
    echo "📋 Méthodes de transfert disponibles :"
    echo "1. Transfert automatique via SCP"
    echo "2. Créer un package pour transfert manuel"
    echo "3. Afficher les instructions manuelles"
    echo ""
    
    read -p "Choisissez une option (1-3): " choice
    
    case $choice in
        1)
            echo ""
            read -p "Adresse IP du serveur (ex: **************): " server_ip
            read -p "Nom d'utilisateur SSH: " username
            read -p "Répertoire de destination [/tmp/odoo-fix]: " destination_path
            
            # Valeur par défaut
            destination_path=${destination_path:-/tmp/odoo-fix}
            
            echo ""
            log_info "Configuration :"
            echo "  Serveur: $server_ip"
            echo "  Utilisateur: $username"
            echo "  Destination: $destination_path"
            echo ""
            
            read -p "Continuer ? (y/N): " confirm
            if [[ "$confirm" =~ ^[Yy]$ ]]; then
                if transfer_via_scp "$server_ip" "$username" "$destination_path"; then
                    show_connection_instructions "$server_ip" "$username" "$destination_path"
                fi
            else
                log_info "Transfert annulé"
            fi
            ;;
        2)
            echo ""
            create_local_package
            ;;
        3)
            echo ""
            echo "📋 === TRANSFERT MANUEL ==="
            echo ""
            echo "1. Copiez ces fichiers vers votre serveur :"
            for file in "${FILES_TO_TRANSFER[@]}"; do
                echo "   - $file"
            done
            echo ""
            echo "2. Méthodes de transfert :"
            echo "   - SCP: scp *.sh *.md utilisateur@serveur:/tmp/"
            echo "   - SFTP: utilisez un client SFTP"
            echo "   - USB: copiez sur clé USB puis transférez"
            echo ""
            echo "3. Sur le serveur, rendez les scripts exécutables :"
            echo "   chmod +x *.sh"
            echo ""
            ;;
        *)
            log_error "Option invalide"
            exit 1
            ;;
    esac
    
    echo ""
    echo "🎉 === TRANSFERT TERMINÉ ==="
    log_success "Les fichiers de correction sont prêts à être utilisés"
    echo ""
}

# Vérifier si les outils nécessaires sont installés
if [[ "$1" != "--skip-checks" ]]; then
    if ! command -v ssh &> /dev/null; then
        log_warning "SSH n'est pas installé ou non disponible"
        log_info "Vous pouvez utiliser l'option 2 pour créer un package"
    fi
    
    if ! command -v scp &> /dev/null; then
        log_warning "SCP n'est pas installé ou non disponible"
        log_info "Vous pouvez utiliser l'option 2 pour créer un package"
    fi
fi

# Exécuter le script principal
main "$@"
