# 🚀 Edara Electron App - Solution Odoo 13

Application Electron pour encapsuler le service Odoo 13 Edara avec **correction automatique du problème ERR_CONTENT_LENGTH_MISMATCH**.

## 🎯 Problème Résolu

Cette version inclut une solution complète pour le problème `ERR_CONTENT_LENGTH_MISMATCH` qui cause des écrans blancs dans Odoo 13.

### Cause du problème
- Utilisation de workers dans Odoo 13 sans reverse proxy
- Assets CSS/JS qui ne se chargent pas correctement
- Interface Odoo inaccessible

### Solution fournie
- ✅ Scripts automatisés de correction
- ✅ Configuration Nginx optimisée
- ✅ Tests de diagnostic intégrés
- ✅ Guide de démarrage rapide

## 📁 Fichiers de Correction

### Scripts Automatisés
- `fix-odoo13-temporary.sh` - Correction rapide (désactive workers)
- `fix-odoo13-permanent.sh` - Solution complète (configure Nginx)
- `test-odoo13-connection.sh` - Test de diagnostic
- `transfer-to-server.sh` - Transfert vers serveur

### Documentation
- `SOLUTION_ODOO13_ERR_CONTENT_LENGTH.md` - Solution détaillée
- `GUIDE_DEMARRAGE_RAPIDE.md` - Guide de démarrage rapide

## 🚀 Correction Rapide (5 minutes)

### 1. Diagnostic
```bash
./test-odoo13-connection.sh
```

### 2. Correction Temporaire
```bash
# Sur le serveur Odoo (**************)
./fix-odoo13-temporary.sh
```

### 3. Solution Permanente (Recommandée)
```bash
# Sur le serveur Odoo
sudo ./fix-odoo13-permanent.sh
```

### 4. Transfert vers Serveur
```bash
# Assistant de transfert
./transfer-to-server.sh
```

## 🚀 Fonctionnalités

- **Connexion Intelligente** : Priorité aux serveurs locaux avec fallback automatique vers le serveur distant
- **Interface Moderne** : Interface de connexion élégante avec support des thèmes sombre/clair
- **Écran de Chargement** : Animation de progression pendant le chargement d'Odoo
- **Gestion des Sessions** : Authentification sécurisée et gestion des cookies de session
- **Multi-Serveurs** : Support de plusieurs serveurs locaux configurables

## 🛠️ Installation

1. **Installer les dépendances** :
```bash
npm install
```

2. **Configurer les serveurs locaux** :
Éditer le fichier `serveur_ip.txt` avec vos adresses IP locales :
```
**************
************
************
```

## 🚀 Utilisation

### Démarrage de l'application
```bash
# Démarrage standard
npm start

# Démarrage optimisé pour Odoo (recommandé)
npm run start-optimized
```

> **Note** : Utilisez `npm run start-optimized` si vous rencontrez des problèmes de chargement avec Odoo (écran blanc, erreurs de ressources).

### Construction pour distribution
```bash
# Pour toutes les plateformes
npm run build

# Pour Windows uniquement
npm run build-win

# Pour macOS uniquement
npm run build-mac

# Pour Linux uniquement
npm run build-linux
```

## ⚙️ Configuration

### Serveurs Locaux
Modifiez le fichier `serveur_ip.txt` pour ajouter vos serveurs locaux :
```
**************
************
************
```

### Serveur Distant
Par défaut : `https://edara.ligne-digitale.com`

### Base de Données
Par défaut : `ligne-digitale`

## 🔄 Logique de Connexion

1. **Test des serveurs locaux** : Test séquentiel des serveurs dans `serveur_ip.txt` (port 8069)
2. **Fallback distant** : Si aucun serveur local disponible, connexion au serveur distant
3. **Authentification** : Via API XML-RPC d'Odoo
4. **Interface** : Chargement dans une fenêtre dédiée avec détection automatique

## 🔒 Sécurité

- **Context Isolation** : Activé pour toutes les fenêtres
- **Node Integration** : Désactivé côté renderer
- **Preload Script** : Communication sécurisée via contextBridge
- **Web Security** : Désactivé uniquement pour la fenêtre Odoo (nécessaire)

## 📦 Distribution

Les fichiers de distribution sont générés dans le dossier `dist/` :
- **Windows** : Installateur NSIS (.exe)
- **macOS** : Image disque (.dmg)
- **Linux** : AppImage (.AppImage)

## 🆘 Support Odoo 13

En cas de problème avec Odoo 13 :
1. Exécutez `./test-odoo13-connection.sh` pour diagnostiquer
2. Consultez `GUIDE_DEMARRAGE_RAPIDE.md` pour une solution rapide
3. Appliquez la correction appropriée selon vos besoins

## 📊 État des Serveurs (Dernière vérification)

- ✅ `**************` - Fonctionnel (nécessite correction ERR_CONTENT_LENGTH_MISMATCH)
- ❌ `************` - Non accessible
- ❌ `************` - Non accessible

## 📄 Licence

MIT License
