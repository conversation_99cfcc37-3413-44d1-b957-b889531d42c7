#!/bin/bash

# Script pour lister tous les fichiers de correction créés

echo "📁 === FICHIERS DE CORRECTION ODOO 13 ==="
echo "Liste complète des fichiers créés pour résoudre ERR_CONTENT_LENGTH_MISMATCH"
echo ""

# Couleurs
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Fonction pour vérifier et afficher un fichier
check_file() {
    local file="$1"
    local description="$2"
    
    if [[ -f "$file" ]]; then
        local size=$(ls -lh "$file" | awk '{print $5}')
        echo -e "${GREEN}✅ $file${NC} ($size) - $description"
    else
        echo -e "${YELLOW}⚠️  $file${NC} - $description (MANQUANT)"
    fi
}

echo -e "${BLUE}🚀 SCRIPTS DE CORRECTION AUTOMATIQUE${NC}"
check_file "fix-remote-odoo13.sh" "Correction automatique via SSH (RECOMMANDÉ)"
check_file "fix-urgent-odoo13.sh" "Script d'urgence pour le serveur"
check_file "fix-odoo13-temporary.sh" "Correction temporaire (désactive workers)"
check_file "fix-odoo13-permanent.sh" "Solution permanente avec Nginx"

echo ""
echo -e "${BLUE}🔍 SCRIPTS DE DIAGNOSTIC${NC}"
check_file "test-odoo13-connection.sh" "Test de connexion et diagnostic"

echo ""
echo -e "${BLUE}📤 SCRIPTS D'ASSISTANCE${NC}"
check_file "transfer-to-server.sh" "Assistant de transfert vers serveur"
check_file "liste-fichiers-correction.sh" "Liste des fichiers (ce script)"

echo ""
echo -e "${BLUE}📚 DOCUMENTATION COMPLÈTE${NC}"
check_file "ACTION_IMMEDIATE.md" "Guide d'action immédiate (2 minutes)"
check_file "SOLUTIONS_FINALES_ODOO13.md" "Toutes les solutions disponibles"
check_file "SOLUTION_ODOO13_ERR_CONTENT_LENGTH.md" "Solution technique détaillée"
check_file "GUIDE_DEMARRAGE_RAPIDE.md" "Guide de démarrage rapide"
check_file "CORRECTION_COMPLETE_ODOO13.md" "Résumé complet de la correction"

echo ""
echo -e "${BLUE}📋 FICHIERS PRINCIPAUX${NC}"
check_file "README.md" "Documentation principale mise à jour"
check_file "serveur_ip.txt" "Configuration des serveurs"

echo ""
echo "🎯 === UTILISATION RECOMMANDÉE ==="
echo ""
echo "1. 🚨 CORRECTION IMMÉDIATE (2 minutes) :"
echo "   ./fix-remote-odoo13.sh"
echo ""
echo "2. 📖 CONSULTER LA DOCUMENTATION :"
echo "   - ACTION_IMMEDIATE.md (pour commencer)"
echo "   - SOLUTIONS_FINALES_ODOO13.md (toutes les options)"
echo ""
echo "3. 🧪 TESTER LA CONNEXION :"
echo "   ./test-odoo13-connection.sh"
echo ""
echo "4. 📤 TRANSFÉRER VERS SERVEUR (si nécessaire) :"
echo "   ./transfer-to-server.sh"
echo ""

# Compter les fichiers
total_files=0
existing_files=0

files=(
    "fix-remote-odoo13.sh"
    "fix-urgent-odoo13.sh" 
    "fix-odoo13-temporary.sh"
    "fix-odoo13-permanent.sh"
    "test-odoo13-connection.sh"
    "transfer-to-server.sh"
    "liste-fichiers-correction.sh"
    "ACTION_IMMEDIATE.md"
    "SOLUTIONS_FINALES_ODOO13.md"
    "SOLUTION_ODOO13_ERR_CONTENT_LENGTH.md"
    "GUIDE_DEMARRAGE_RAPIDE.md"
    "CORRECTION_COMPLETE_ODOO13.md"
    "README.md"
    "serveur_ip.txt"
)

for file in "${files[@]}"; do
    ((total_files++))
    if [[ -f "$file" ]]; then
        ((existing_files++))
    fi
done

echo ""
echo "📊 === STATISTIQUES ==="
echo "Fichiers présents : $existing_files/$total_files"

if [[ $existing_files -eq $total_files ]]; then
    echo -e "${GREEN}✅ Tous les fichiers de correction sont présents${NC}"
    echo ""
    echo "🚀 Vous êtes prêt à résoudre le problème ERR_CONTENT_LENGTH_MISMATCH !"
else
    echo -e "${YELLOW}⚠️  Certains fichiers sont manquants${NC}"
    echo "Vérifiez que tous les scripts ont été créés correctement."
fi

echo ""
echo "🎉 === PRÊT À CORRIGER ODOO 13 ==="
echo ""
