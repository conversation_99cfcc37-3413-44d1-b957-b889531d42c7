#!/bin/bash

# Script de test de connexion Odoo 13
# Vérifie si le problème ERR_CONTENT_LENGTH_MISMATCH est résolu

echo "🔍 === TEST DE CONNEXION ODOO 13 ==="
echo "Vérification de la résolution du problème ERR_CONTENT_LENGTH_MISMATCH"
echo ""

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Serveurs à tester (depuis serveur_ip.txt)
SERVERS=(
    "**************"
    "************"
    "************"
)

# Fonction pour tester une URL
test_url() {
    local url="$1"
    local description="$2"
    
    log_info "Test: $description"
    log_info "URL: $url"
    
    # Test avec curl
    local response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 10 "$url" 2>/dev/null)
    
    if [[ "$response" =~ ^[23] ]]; then
        log_success "✅ Réponse HTTP: $response"
        return 0
    elif [[ "$response" =~ ^[45] ]]; then
        log_warning "⚠️  Réponse HTTP: $response (serveur accessible mais erreur)"
        return 1
    else
        log_error "❌ Pas de réponse ou erreur de connexion"
        return 2
    fi
}

# Fonction pour tester les assets web
test_web_assets() {
    local server_ip="$1"
    local base_url="http://$server_ip:8069"
    
    echo ""
    log_info "=== TEST DES ASSETS WEB POUR $server_ip ==="
    
    # URLs critiques à tester
    local urls=(
        "$base_url/web/database/selector"
        "$base_url/web/static/src/css/base.css"
        "$base_url/web/static/src/js/boot.js"
        "$base_url/web/content/assets_common.css"
        "$base_url/web/content/assets_backend.css"
    )
    
    local success_count=0
    local total_count=${#urls[@]}
    
    for url in "${urls[@]}"; do
        if test_url "$url" "Asset web"; then
            ((success_count++))
        fi
        echo ""
    done
    
    echo "📊 Résultat: $success_count/$total_count assets accessibles"
    
    if [[ $success_count -eq $total_count ]]; then
        log_success "🎉 Tous les assets sont accessibles"
        return 0
    elif [[ $success_count -gt 0 ]]; then
        log_warning "⚠️  Certains assets sont inaccessibles"
        return 1
    else
        log_error "❌ Aucun asset accessible"
        return 2
    fi
}

# Fonction pour tester la page de connexion Odoo
test_odoo_login_page() {
    local server_ip="$1"
    local url="http://$server_ip:8069/web/login"
    
    log_info "=== TEST DE LA PAGE DE CONNEXION ODOO ==="
    
    # Télécharger la page et vérifier le contenu
    local content=$(curl -s --connect-timeout 10 "$url" 2>/dev/null)
    local http_code=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 10 "$url" 2>/dev/null)
    
    if [[ "$http_code" =~ ^[23] ]]; then
        log_success "✅ Page de connexion accessible (HTTP: $http_code)"
        
        # Vérifier si la page contient les éléments Odoo
        if echo "$content" | grep -q "odoo\|login\|database" -i; then
            log_success "✅ Contenu Odoo détecté"
            
            # Vérifier s'il y a des erreurs JavaScript
            if echo "$content" | grep -q "odoo.define\|web.assets" -i; then
                log_success "✅ Assets JavaScript détectés"
                return 0
            else
                log_warning "⚠️  Assets JavaScript non détectés"
                return 1
            fi
        else
            log_error "❌ Contenu Odoo non détecté"
            return 2
        fi
    else
        log_error "❌ Page de connexion inaccessible (HTTP: $http_code)"
        return 2
    fi
}

# Fonction pour vérifier les services
check_services() {
    log_info "=== VÉRIFICATION DES SERVICES ==="
    
    # Vérifier Nginx
    if systemctl is-active --quiet nginx 2>/dev/null; then
        log_success "✅ Nginx est actif"
    else
        log_warning "⚠️  Nginx n'est pas actif ou non installé"
    fi
    
    # Vérifier Odoo
    local odoo_services=("odoo" "odoo13" "odoo-server" "odoo13-server")
    local odoo_found=false
    
    for service in "${odoo_services[@]}"; do
        if systemctl is-active --quiet "$service" 2>/dev/null; then
            log_success "✅ Service Odoo actif: $service"
            odoo_found=true
            break
        fi
    done
    
    if [[ "$odoo_found" == false ]]; then
        log_warning "⚠️  Aucun service Odoo actif détecté"
    fi
    
    echo ""
}

# Fonction principale de test
main() {
    echo ""
    log_info "Début des tests de connexion..."
    
    # Vérifier les services
    check_services
    
    # Tester chaque serveur
    local working_servers=()
    
    for server_ip in "${SERVERS[@]}"; do
        echo ""
        echo "🖥️  === TEST DU SERVEUR $server_ip ==="
        
        # Test de base
        if test_url "http://$server_ip:8069" "Connexion de base"; then
            working_servers+=("$server_ip")
            
            # Test des assets web
            test_web_assets "$server_ip"
            
            echo ""
            
            # Test de la page de connexion
            test_odoo_login_page "$server_ip"
        fi
        
        echo ""
        echo "----------------------------------------"
    done
    
    # Résumé final
    echo ""
    echo "📋 === RÉSUMÉ DES TESTS ==="
    
    if [[ ${#working_servers[@]} -gt 0 ]]; then
        log_success "✅ Serveurs fonctionnels: ${working_servers[*]}"
        
        echo ""
        log_info "🎯 RECOMMANDATIONS:"
        echo "1. Utilisez un des serveurs fonctionnels dans votre application"
        echo "2. Si vous voyez encore des erreurs ERR_CONTENT_LENGTH_MISMATCH:"
        echo "   - Exécutez ./fix-odoo13-temporary.sh pour une solution rapide"
        echo "   - Ou ./fix-odoo13-permanent.sh pour une solution complète"
        
    else
        log_error "❌ Aucun serveur Odoo accessible"
        
        echo ""
        log_info "🔧 ACTIONS À ENTREPRENDRE:"
        echo "1. Vérifiez que les services Odoo sont démarrés"
        echo "2. Vérifiez la configuration réseau"
        echo "3. Exécutez ./fix-odoo13-temporary.sh pour résoudre les problèmes de workers"
    fi
    
    echo ""
    echo "🏁 === FIN DES TESTS ==="
}

# Vérifier si curl est installé
if ! command -v curl &> /dev/null; then
    log_error "curl n'est pas installé. Installation requise pour les tests."
    echo "Ubuntu/Debian: sudo apt install curl"
    echo "CentOS/RHEL: sudo yum install curl"
    exit 1
fi

# Exécuter les tests
main "$@"
